import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Tabs,
  Badge,
  Typography
} from 'antd';
import {
  ShoppingOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

// Import modular components
import ProductStatistics from './products/ProductStatistics';
import ProductTable from './products/ProductTable';
import PendingProductsTable from './products/PendingProductsTable';
import ProductFilters from './products/ProductFilters';
import ProductApprovalModal from './products/ProductApprovalModal';
import { useProductActions } from './products/useProductActions';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const ProductsManagement = () => {
  // State management
  const [products, setProducts] = useState([]);
  const [pendingProducts, setPendingProducts] = useState([]);
  const [allProducts, setAllProducts] = useState([]);
  const [stats, setStats] = useState({});
  const [activeTab, setActiveTab] = useState('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: '',
    category: '',
    vendor: '',
    search: ''
  });
  
  // Modal state
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [approvalModalVisible, setApprovalModalVisible] = useState(false);
  const [approvalAction, setApprovalAction] = useState('');
  
  const [approvalForm] = Form.useForm();
  
  // Use custom hook for actions
  const {
    loading,
    fetchProducts: apiFetchProducts,
    fetchAllProducts: apiFetchAllProducts,
    fetchPendingProducts: apiFetchPendingProducts,
    fetchStats: apiFetchStats,
    updateStatus,
    toggleFeatured,
    approveProduct,
    rejectProduct,
    requestChanges
  } = useProductActions();

  // Load data on component mount and when dependencies change
  useEffect(() => {
    loadData();
  }, [pagination.current, pagination.pageSize, filters, activeTab]);

  const loadData = async () => {
    const [productsResult, pendingResult, allProductsResult, statsResult] = await Promise.all([
      apiFetchProducts(pagination, filters),
      apiFetchPendingProducts(),
      apiFetchAllProducts(),
      apiFetchStats()
    ]);
    
    setProducts(productsResult.products);
    setPagination(productsResult.pagination);
    setPendingProducts(pendingResult);
    setAllProducts(allProductsResult);
    setStats(statsResult);
  };

  // Event handlers
  const handlePaginationChange = (page, pageSize) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize
    }));
  };

  const handleStatusChange = async (productId, newStatus) => {
    const success = await updateStatus(productId, newStatus);
    if (success) {
      loadData();
    }
  };

  const handleToggleFeatured = async (productId, featured) => {
    const success = await toggleFeatured(productId, featured);
    if (success) {
      loadData();
    }
  };

  const handleApprovalAction = (product, action) => {
    setSelectedProduct(product);
    setApprovalAction(action);
    setApprovalModalVisible(true);
    
    if (action === 'approve') {
      approvalForm.setFieldsValue({ notes: '' });
    } else if (action === 'reject') {
      approvalForm.setFieldsValue({ reason: '', notes: '' });
    }
  };

  const handleApprovalSubmit = async (values) => {
    let success = false;
    
    switch (approvalAction) {
      case 'approve':
        success = await approveProduct(selectedProduct._id, { notes: values.notes });
        break;
      case 'reject':
        success = await rejectProduct(selectedProduct._id, {
          reason: values.reason,
          notes: values.notes
        });
        break;
      case 'request_changes':
        success = await requestChanges(selectedProduct._id, {
          changes: values.changes,
          notes: values.notes
        });
        break;
    }

    if (success) {
      setApprovalModalVisible(false);
      approvalForm.resetFields();
      loadData();
    }
  };

  const handleModalCancel = () => {
    setApprovalModalVisible(false);
    approvalForm.resetFields();
    setSelectedProduct(null);
  };

  return (
    <div style={{ 
      padding: '0 16px',
      maxWidth: '100%',
      overflow: 'hidden'
    }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ 
          marginBottom: '16px',
          fontSize: 'clamp(1.5rem, 4vw, 2rem)'
        }}>Products Management</Title>
        <Text type="secondary">
          Manage all products, approvals, and inventory across your platform
        </Text>
      </div>

      {/* Statistics Cards */}
      <ProductStatistics 
        stats={stats}
        pendingCount={pendingProducts.length}
        loading={loading}
        allProducts={allProducts}
      />

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane 
            tab={
              <span>
                <ShoppingOutlined />
                All Products
              </span>
            } 
            key="all"
          >
            <ProductFilters
              filters={filters}
              onFiltersChange={setFilters}
              onRefresh={loadData}
              loading={loading}
            />

            <ProductTable
              products={products}
              loading={loading}
              pagination={pagination}
              onPaginationChange={handlePaginationChange}
              onStatusChange={handleStatusChange}
              onToggleFeatured={handleToggleFeatured}
              onViewDetails={(product) => {/* TODO: Implement view details */}}
            />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ClockCircleOutlined />
                Pending Approval
                {pendingProducts.length > 0 && (
                  <Badge count={pendingProducts.length} style={{ marginLeft: '8px' }} />
                )}
              </span>
            }
            key="pending"
          >
            <PendingProductsTable
              products={pendingProducts}
              loading={loading}
              onApprovalAction={handleApprovalAction}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Approval Modal */}
      <ProductApprovalModal
        visible={approvalModalVisible}
        selectedProduct={selectedProduct}
        approvalAction={approvalAction}
        form={approvalForm}
        loading={loading}
        onSubmit={handleApprovalSubmit}
        onCancel={handleModalCancel}
      />
    </div>
  );
};

export default ProductsManagement;
