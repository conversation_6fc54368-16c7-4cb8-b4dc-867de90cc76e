import React from 'react';
import { Row, Col, Card, Statistic } from 'antd';
import {
  ShoppingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

const ProductStatistics = ({ 
  stats, 
  pendingCount, 
  loading,
  allProducts = [] 
}) => {
  // Calculate dynamic statistics from actual data as fallback
  const calculateDynamicStats = () => {
    const totalProducts = allProducts.length;
    const activeProducts = allProducts.filter(product => product.status === 'active').length;
    const lowStockProducts = allProducts.filter(product => {
      if (product.inventory?.trackQuantity) {
        return product.inventory.quantity <= (product.inventory.lowStockThreshold || 5);
      }
      return false;
    }).length;

    return {
      totalProducts,
      activeProducts,
      lowStockProducts
    };
  };

  const dynamicStats = calculateDynamicStats();

  return (
    <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Total Products"
            value={stats.totalProducts || dynamicStats.totalProducts || 0}
            prefix={<ShoppingOutlined />}
            valueStyle={{ color: '#1890ff' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Pending Approval"
            value={pendingCount}
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Active Products"
            value={stats.activeProducts || dynamicStats.activeProducts || 0}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
            loading={loading}
          />
        </Card>
      </Col>
      <Col xs={24} sm={12} md={6}>
        <Card>
          <Statistic
            title="Low Stock"
            value={stats.lowStockProducts || dynamicStats.lowStockProducts || 0}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
            loading={loading}
          />
        </Card>
      </Col>
    </Row>
  );
};

export default ProductStatistics;
