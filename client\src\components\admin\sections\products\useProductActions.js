import { useState } from 'react';
import { message } from 'antd';
import { productsApi } from '../../../../services/adminApi';

export const useProductActions = () => {
  const [loading, setLoading] = useState(false);

  const fetchProducts = async (pagination, filters) => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters
      };

      const response = await productsApi.getAll(params);
      if (response.data.success) {
        return {
          products: response.data.data.products,
          pagination: {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: response.data.data.pagination.totalProducts
          }
        };
      }
      return { products: [], pagination };
    } catch (error) {
      message.error('Failed to fetch products');
      console.error('Error fetching products:', error);
      return { products: [], pagination };
    } finally {
      setLoading(false);
    }
  };

  const fetchAllProducts = async () => {
    try {
      const response = await productsApi.getAll({ 
        page: 1, 
        limit: 1000, // Get a large number to include all products
        status: '' // No status filter to get all products
      });
      if (response.data.success) {
        return response.data.data.products;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch all products for statistics:', error);
      return [];
    }
  };

  const fetchPendingProducts = async () => {
    try {
      const response = await productsApi.getPendingApproval();
      if (response.data.success) {
        return response.data.data.products;
      }
      return [];
    } catch (error) {
      console.error('Failed to fetch pending products:', error);
      return [];
    }
  };

  const fetchStats = async () => {
    try {
      const response = await productsApi.getStats();
      if (response.data.success) {
        return response.data.data;
      }
      return {};
    } catch (error) {
      console.error('Failed to fetch stats:', error);
      return {};
    }
  };

  const updateStatus = async (productId, newStatus) => {
    try {
      setLoading(true);
      const response = await productsApi.updateStatus(productId, { status: newStatus });
      if (response.data.success) {
        message.success('Product status updated successfully');
        return true;
      }
      return false;
    } catch (error) {
      message.error('Failed to update product status');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const toggleFeatured = async (productId, featured) => {
    try {
      setLoading(true);
      const response = await productsApi.toggleFeatured(productId, { featured: !featured });
      if (response.data.success) {
        message.success(`Product ${!featured ? 'featured' : 'unfeatured'} successfully`);
        return true;
      }
      return false;
    } catch (error) {
      message.error('Failed to update featured status');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const approveProduct = async (productId, data) => {
    try {
      setLoading(true);
      const response = await productsApi.approve(productId, data);
      if (response.data.success) {
        message.success('Product approved successfully');
        return true;
      }
      return false;
    } catch (error) {
      message.error('Failed to approve product');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const rejectProduct = async (productId, data) => {
    try {
      setLoading(true);
      const response = await productsApi.reject(productId, data);
      if (response.data.success) {
        message.success('Product rejected successfully');
        return true;
      }
      return false;
    } catch (error) {
      message.error('Failed to reject product');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const requestChanges = async (productId, data) => {
    try {
      setLoading(true);
      const response = await productsApi.requestChanges(productId, data);
      if (response.data.success) {
        message.success('Changes requested successfully');
        return true;
      }
      return false;
    } catch (error) {
      message.error('Failed to request changes');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    fetchProducts,
    fetchAllProducts,
    fetchPendingProducts,
    fetchStats,
    updateStatus,
    toggleFeatured,
    approveProduct,
    rejectProduct,
    requestChanges
  };
};
